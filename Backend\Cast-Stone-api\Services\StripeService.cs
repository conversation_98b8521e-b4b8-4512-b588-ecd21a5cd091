﻿using Cast_Stone_api.Domain.Models.PaymentGatewaySettings;
using Microsoft.Extensions.Options;
using Stripe;

public class StripeService
{
    private readonly StripeSettings _settings;

    public StripeService(IOptions<StripeSettings> stripeOptions)
    {
        _settings = stripeOptions.Value;
        StripeConfiguration.ApiKey = _settings.SecretKey;
    }

    public async Task<string> CreatePaymentIntentAsync(long amount, string currency)
    {
        var options = new PaymentIntentCreateOptions
        {
            Amount = amount,
            Currency = currency,
            AutomaticPaymentMethods = new PaymentIntentAutomaticPaymentMethodsOptions
            {
                Enabled = true,
            }
        };

        var service = new PaymentIntentService();
        var intent = await service.CreateAsync(options);
        return intent.ClientSecret;
    }
}

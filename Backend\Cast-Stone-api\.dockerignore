# .dockerignore file for Cast Stone API

# Build output directories
bin/
obj/
out/

# Visual Studio files
.vs/
*.user
*.suo
*.userosscache
*.sln.docstates
*.vsidx
*.v2

# IDE files
.vscode/
.idea/

# Logs
*.log
logs/

# Runtime files
*.dll
*.exe
*.pdb
*.cache

# Database files
*.db
*.sqlite
*.sqlite3

# Environment files (these should be set via Railway environment variables)
.env
.env.*
appsettings.Development.json

# Git files
.git/
.gitignore
.gitattributes

# Documentation
README.md
*.md

# Scripts (not needed in container)
Scripts/

# Test files
**/TestResults/
**/*test*/
**/*Test*/

# Temporary files
*.tmp
*.swp
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Node.js (if any)
node_modules/
npm-debug.log*

# Coverage reports
coverage/

# Backup files
*.bak
*.backup

# Migration files (these will be run separately)
Migrations/

# HTTP test files
*.http

# Solution files (not needed in container)
*.sln

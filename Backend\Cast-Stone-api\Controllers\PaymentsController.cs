﻿using Cast_Stone_api.DTOs.Request;
using Microsoft.AspNetCore.Mvc;

[ApiController]
[Route("api/[controller]")]
public class PaymentsController : ControllerBase
{
    private readonly StripeService _stripeService;

    public PaymentsController(StripeService stripeService)
    {
        _stripeService = stripeService;
    }

    [HttpPost("create-intent")]
    public async Task<IActionResult> CreateIntent([FromBody] PaymentRequest request)
    {
        var clientSecret = await _stripeService.CreatePaymentIntentAsync(request.Amount, request.Currency);
        return Ok(new { clientSecret });
    }
}
